{"version": "2.0.0", "tasks": [{"label": "Run Application", "type": "shell", "command": "make", "args": ["run"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$rustc"}, {"label": "Watch and Run", "type": "shell", "command": "make", "args": ["watch"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$rustc", "isBackground": true}, {"label": "Build", "type": "shell", "command": "make", "args": ["build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$rustc"}, {"label": "Clean", "type": "shell", "command": "make", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "Build Linux AMD64", "type": "shell", "command": "make", "args": ["build-linux"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$rustc"}, {"label": "Build macOS ARM64", "type": "shell", "command": "make", "args": ["build-macos"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$rustc"}, {"label": "Build All Platforms", "type": "shell", "command": "make", "args": ["build-all"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$rustc"}, {"label": "Install Cross-compilation Targets", "type": "shell", "command": "make", "args": ["install-targets"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "Format Code", "type": "shell", "command": "make", "args": ["fmt"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "Check Format", "type": "shell", "command": "make", "args": ["fmt-check"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "<PERSON>", "type": "shell", "command": "make", "args": ["clippy"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$rustc"}, {"label": "Fix <PERSON>", "type": "shell", "command": "make", "args": ["clippy-fix"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$rustc"}, {"label": "Lint All", "type": "shell", "command": "make", "args": ["lint"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$rustc"}, {"label": "Fix All Issues", "type": "shell", "command": "make", "args": ["fix"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$rustc"}]}