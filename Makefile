CARGO = cargo
LINUX_TARGET = x86_64-unknown-linux-gnu
MACOS_TARGET = aarch64-apple-darwin
TARGET_DIR = target
RELEASES_DIR = releases

.PHONY: all
all: run

.PHONY: run
run:
	$(CARGO) run

.PHONY: watch
watch:
	$(CARGO) watch -x run

.PHONY: build
build:
	$(CARGO) build --release
	@mkdir -p $(RELEASES_DIR)
	@find $(TARGET_DIR)/release -maxdepth 1 -type f -perm +111 -not -name "*.d" -exec cp {} $(RELEASES_DIR)/ \;

.PHONY: clean
clean:
	$(CARGO) clean
	rm -rf $(RELEASES_DIR)

.PHONY: build-linux
build-linux:
	$(CARGO) build --release --target $(LINUX_TARGET)
	@mkdir -p $(RELEASES_DIR)
	@find $(TARGET_DIR)/$(LINUX_TARGET)/release -maxdepth 1 -type f -perm +111 -not -name "*.d" -exec sh -c 'cp "$$1" "$(RELEASES_DIR)/$$(basename "$$1")-linux-amd64"' _ {} \;

.PHONY: build-macos
build-macos:
	$(CARGO) build --release --target $(MACOS_TARGET)
	@mkdir -p $(RELEASES_DIR)
	@find $(TARGET_DIR)/$(MACOS_TARGET)/release -maxdepth 1 -type f -perm +111 -not -name "*.d" -exec sh -c 'cp "$$1" "$(RELEASES_DIR)/$$(basename "$$1")-macos-arm64"' _ {} \;

.PHONY: build-all
build-all: build-linux build-macos

.PHONY: install-targets
install-targets:
	rustup target add $(LINUX_TARGET)
	rustup target add $(MACOS_TARGET)

.PHONY: fmt
fmt:
	$(CARGO) fmt

.PHONY: fmt-check
fmt-check:
	$(CARGO) fmt --check

.PHONY: clippy
clippy:
	$(CARGO) clippy -- -D warnings

.PHONY: clippy-fix
clippy-fix:
	$(CARGO) clippy --fix --allow-dirty --allow-staged

.PHONY: lint
lint: fmt-check clippy

.PHONY: fix
fix: fmt clippy-fix

.PHONY: help
help:
	@echo "Available targets:"
	@echo "  run           - Run the application in development mode"
	@echo "  watch         - Watch for changes and auto-restart"
	@echo "  build         - Build for current platform and copy to releases/"
	@echo "  clean         - Clean build artifacts and releases/"
	@echo "  build-linux   - Cross-compile for Linux AMD64 and copy to releases/"
	@echo "  build-macos   - Cross-compile for macOS ARM64 and copy to releases/"
	@echo "  build-all     - Build for all platforms and copy to releases/"
	@echo "  install-targets - Install cross-compilation targets"
	@echo "  fmt           - Format code with rustfmt"
	@echo "  fmt-check     - Check code formatting"
	@echo "  clippy        - Run clippy linter"
	@echo "  clippy-fix    - Auto-fix clippy issues"
	@echo "  lint          - Run all linting (fmt-check + clippy)"
	@echo "  fix           - Auto-fix all issues (fmt + clippy-fix)"
	@echo "  help          - Show this help message"
