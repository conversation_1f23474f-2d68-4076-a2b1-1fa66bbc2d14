# Tổng quan

Dự án này là 1 ứng dụng **CLI đơn giản** đ<PERSON><PERSON><PERSON> viết bằng **Rust phiên bản ổn định mới nhất**, nó đóng vai trò như 1 **cầu nối trung gian** giữa dịch vụ **[Jito Shredstream](https://docs.jito.wtf/lowlatencytxnfeed)** (hoặc các dịch vụ tương tự) và các client muốn nhận được dữ liệu **đã được giải** mã từ **Jito Shredstream Shred** thành các **giao dịch có thể đọc được**.

# Chức năng chính

Ứng dụng này sẽ kết nối đến **nhiều endpoint của JitoShredstream cùng 1 lúc**, lắng nghe các **shred** đư<PERSON><PERSON> gửi đến (**lọc các shred trùng lặp** khi chúng cùng đến từ **các endpoint khác nhau**), **giải mã** và **lấy** các giao dịch có chứa trong các shreds, **lọc** và **xử lý** giao dịch để **loại bỏ** các giao dịch **không phù hợp**, **thêm các thông tin hữu ích** vào giao dịch, sau đó **stream** chúng cho các client đang lắng nghe thông qua giao thức **WebSocket**.

# Luồng hoạt động

1. Kết nối đến các endpoints thông qua giao thức GRPC ([Proto](protos/shredstream.proto))
2. Gửi 1 subscribe request (**SubscribeEntriesRequest**) với filter (**SubscribeRequestFilterAccounts**) chứa danh sách các accounts muốn lắng nghe tới các luồng stream đã kết nối
3. Lắng nghe các shred được gửi đến, giải mã các shred này với những thông tin cơ bản nhất để lấy được identify của mỗi shred, loại bỏ các shred trùng lặp dựa trên identify, sau đó giải mã nốt các thông tin khác trong các shred đã nhận được
4. Xử lý dữ liệu đã giải mã thành các custom **VersionedTransaction** với các thông tin hữu ích
5. Lọc các custom transaction để chỉ lấy những transaction có chứa các accounts chúng ta đã subscribe
6. Broadcast cho các client đang lắng nghe thông qua **WebSocket**

# Mục tiêu dự án

-   Dữ liệu từ khi nhận được từ Jito Shredstream đến khi broadcast cho user phải nhanh nhất có thể (nanoseconds)
-   Dữ liệu phải chính xác 100%
-   Xử lý được hàng trăm nghìn shred cùng 1 lúc
-   Tận dụng được tối đa hiệu năng của máy tính
-   Tối ưu tốt cho production
-   Chạy liên tục 24/7, zero downtime
-   Code đơn giản, ngắn gọn nhưng tối ưu, đáp ứng tốt nhu cầu

# Yêu cầu

-   Tuân thủ nghiêm ngặt các quy định của dự án
-   Tôn trọng cấu trúc thư mục, file có sẵn, không được tự ý thay đổi khi chưa có sự cho phép
-   Sử dụng Rust phiên bản ổn định mới nhất hiện tại
-   Sử dụng các thư viện được cập nhật thường xuyên nhất, có nhiều lượt tải nhất, và được cộng đồng tin tưởng và đánh giá cao nhất
-   Không cài quá nhiều thư viện, chỉ sử dụng những thư viện thật sự cần thiết cho ứng dụng
-   Tách các phần có thể tái sử dụng vào thư mục `utils` và chia chúng thành nhiều file theo loại của mỗi function đó

# Cấu trúc thư mục

```
shreder/
├── src/                    # Mã nguồn chính của ứng dụng
│   ├── main.rs            # Entry point của ứng dụng
│   ├── common/            # Các module chung
│   │   ├── mod.rs         # Module declarations
│   │   ├── broadcaster.rs # WebSocket broadcasting logic
│   │   ├── client.rs      # GRPC client implementation
│   │   ├── monitor.rs     # System monitoring utilities
│   │   ├── processor.rs   # Shred processing logic
│   │   └── server.rs      # Server management
│   ├── config/            # Cấu hình ứng dụng
│   │   ├── mod.rs         # Module declarations
│   │   ├── endpoints.rs   # GRPC endpoints configuration
│   │   ├── filters.rs     # Transaction filtering rules
│   │   ├── logger.rs      # Logging configuration
│   │   ├── monitor.rs     # Monitoring configuration
│   │   └── server.rs      # Server configuration
│   ├── core/              # Core functionality
│   │   ├── mod.rs         # Module declarations
│   │   ├── config.rs      # Core configuration management
│   │   └── logger.rs      # Core logging implementation
│   └── utils/             # Utility functions có thể tái sử dụng
│       └── mod.rs         # Module declarations
├── protos/                # Protocol Buffer definitions
│   └── shredstream.proto  # GRPC service definitions cho Jito Shredstream
├── releases/              # Binary files sau khi build
│   ├── shreder            # Binary cho platform hiện tại
│   ├── shreder-linux-amd64 # Binary cho Linux AMD64
│   └── shreder-macos-arm64 # Binary cho macOS ARM64
├── target/                # Cargo build artifacts (auto-generated)
├── .vscode/               # VSCode workspace settings
├── .cargo/                # Cargo configuration
├── Cargo.toml             # Package configuration và dependencies
├── Cargo.lock             # Dependency lock file (auto-generated)
├── Makefile               # Build scripts và automation commands
├── clippy.toml            # Clippy linting configuration
└── README.md              # Tài liệu dự án
```

## Mô tả chi tiết các thư mục

### `src/common/`

Chứa các module chung được sử dụng xuyên suốt ứng dụng:

-   **broadcaster.rs**: Logic broadcast dữ liệu qua WebSocket cho clients
-   **client.rs**: Implementation GRPC client để kết nối với Jito Shredstream
-   **monitor.rs**: Utilities để monitor hiệu năng và trạng thái hệ thống
-   **processor.rs**: Logic xử lý và giải mã shred thành transactions
-   **server.rs**: Quản lý server và connection handling

### `src/config/`

Chứa các module cấu hình cho từng thành phần:

-   **endpoints.rs**: Cấu hình các GRPC endpoints của Jito Shredstream
-   **filters.rs**: Định nghĩa rules để filter entry khi subscribe đến Jito Shredstream
-   **logger.rs**: Cấu hình logging system
-   **monitor.rs**: Cấu hình monitoring và metrics
-   **server.rs**: Cấu hình WebSocket server

### `src/core/`

Chứa core functionality không phụ thuộc vào business logic:

-   **config.rs**: Core configuration management
-   **logger.rs**: Core logging implementation

### `src/utils/`

Chứa các utility functions có thể tái sử dụng, được tổ chức theo loại function

# Tài liệu tham khảo

-   [Jito Shredstream](https://docs.jito.wtf/lowlatencytxnfeed)
-   [Shredstream Client Repository](https://github.com/jito-labs/shredstream-proxy)
-   [Example To Decode Entry From Shred](https://raw.githubusercontent.com/jito-labs/shredstream-proxy/refs/heads/master/examples/deshred.rs)
-   [Solana Agave Validator Document](https://docs.anza.xyz/)
-   [Solana Agave Client](https://github.com/anza-xyz/agave)
-   [Solana Entry Struct Document in solana_entry crate](https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html)
-   [solana_entry crate sourcecode](https://github.com/anza-xyz/agave/tree/master/entry)

# Lưu ý

-   Nếu cần đọc các file nằm ngoài workspace của dự án này, sử dụng MCP server **File** đã được cài sẵn
-   Với các file nằm trong phạm vi workspace, ưu tiên sử dụng các **buitin tool**
-   Sử dụng MCP server **Github** khi cần truy cập vào các repository trên Github
-   Sử dụng **builtin Web Browser** với các liên kết không thể đọc được bằng MCP server **Fetch**
